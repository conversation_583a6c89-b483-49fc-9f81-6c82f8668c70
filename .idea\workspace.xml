<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="53bca7a6-a998-4a35-ab70-dab1c50302f0" name="更改" comment="update">
      <change beforePath="$PROJECT_DIR$/.cursor/rules/协作式开发规则 (Neko-Dev Collaborative Rule).md" beforeDir="false" afterPath="$PROJECT_DIR$/.cursor/rules/协作式开发规则 (Neko-Dev Collaborative Rule).md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/mcp.json" beforeDir="false" afterPath="$PROJECT_DIR$/mcp.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;huazz233&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/huazz233/huazz-rules.git&quot;,
    &quot;accountId&quot;: &quot;8a1340bc-98c1-467f-aa42-dddc91076c64&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30a8CGMyimOT8s4D3YQNuPoIvmZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/huazz-rules&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\huazz-rules" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考\工作流" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考\Blue-Soul-commits" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\2025\7\30" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考\cunzhi" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.122" />
        <option value="bundled-python-sdk-880ecab49056-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.122" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="53bca7a6-a998-4a35-ab70-dab1c50302f0" name="更改" comment="" />
      <created>1753854194159</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753854194159</updated>
      <workItem from="1753854195270" duration="17167000" />
      <workItem from="1753944524559" duration="1851000" />
      <workItem from="1754291939493" duration="2490000" />
    </task>
    <task id="LOCAL-00001" summary="add ..">
      <option name="closed" value="true" />
      <created>1753856181859</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753856181859</updated>
    </task>
    <task id="LOCAL-00002" summary="添加目前的mcp配置">
      <option name="closed" value="true" />
      <created>1753858473169</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753858473169</updated>
    </task>
    <task id="LOCAL-00003" summary="整理">
      <option name="closed" value="true" />
      <created>1753861327484</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753861327484</updated>
    </task>
    <task id="LOCAL-00004" summary="111">
      <option name="closed" value="true" />
      <created>1753865950972</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753865950972</updated>
    </task>
    <task id="LOCAL-00005" summary="111">
      <option name="closed" value="true" />
      <created>1753866169059</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753866169059</updated>
    </task>
    <task id="LOCAL-00006" summary="111">
      <option name="closed" value="true" />
      <created>1753866290003</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753866290003</updated>
    </task>
    <task id="LOCAL-00007" summary="111">
      <option name="closed" value="true" />
      <created>1753869473209</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753869473209</updated>
    </task>
    <task id="LOCAL-00008" summary="111">
      <option name="closed" value="true" />
      <created>1753869921042</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753869921042</updated>
    </task>
    <task id="LOCAL-00009" summary="111">
      <option name="closed" value="true" />
      <created>1753870718993</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753870718993</updated>
    </task>
    <task id="LOCAL-00010" summary="add .cursor">
      <option name="closed" value="true" />
      <created>1753871503971</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753871503971</updated>
    </task>
    <task id="LOCAL-00011" summary="update">
      <option name="closed" value="true" />
      <created>1753945468966</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753945468966</updated>
    </task>
    <task id="LOCAL-00012" summary="update">
      <option name="closed" value="true" />
      <created>1754294061247</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1754294061247</updated>
    </task>
    <option name="localTasksCounter" value="13" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="add .." />
    <MESSAGE value="chore(mcp): 整理配置并完善文档&#10;&#10;精简 MCP 配置，重构介绍文档为结构化格式。" />
    <MESSAGE value="chore(mcp): 整理配置并完善文档&#10;&#10;精简 MCP 配置，重构介绍文档为结构化格式。" />
    <MESSAGE value="refactor(docs): 重构项目目录结构并统一资源来源说明&#10;&#10;将分散目录整合到&quot;参考资料&quot;下，统一资源来源文档，新增项目README。&#10;&#10;1. 目录重构: 整合&quot;参考&quot;、&quot;在用&quot;、&quot;猫娘&quot;等目录到&quot;参考资料&quot;下分类管理&#10;2. 文档整合: 合并各子目录来源文档为统一的&quot;参考资料/来源.md&quot;&#10;3. 项目文档: 新增根目录README.md" />
    <MESSAGE value="refactor(docs): 重构项目目录结构并统一资源来源说明&#10;&#10;将分散目录整合到&quot;参考资料&quot;下，统一资源来源文档，新增项目README。&#10;&#10;1. 目录重构: 整合&quot;参考&quot;、&quot;在用&quot;、&quot;猫娘&quot;等目录到&quot;参考资料&quot;下分类管理&#10;2. 文档整合: 合并各子目录来源文档为统一的&quot;参考资料/来源.md&quot;&#10;3. 项目文档: 新增根目录README.md" />
    <MESSAGE value="refactor(docs): 重构提交规范文档为AI工具执行指南&#10;&#10;将原有的团队Git协作规范重构为专门的AI助手工作指南，明确定义了AI在分析提交和生成规范信息时的执行原则、知识库和工作流程。&#10;&#10;1. 文档结构重组: 采用执行原则、知识库、记忆机制、使用方式四个核心部分&#10;2. 功能重新定位: 从团队规范转为AI工具的专用执行指南&#10;3. 工作流标准化: 明确定义了从接收输入到生成输出的完整流程&#10;4. 输出格式规范: 统一了提交信息和代码审查报告的标准格式&#10;5. 使用指南完善: 区分最新提交和历史提交的不同修改方式" />
    <MESSAGE value="refactor(docs): 重构提交规范文档为AI工具执行指南&#10;&#10;将原有的团队Git协作规范重构为专门的AI助手工作指南，明确定义了AI在分析提交和生成规范信息时的执行原则、知识库和工作流程。&#10;&#10;1. 文档结构重组: 采用执行原则、知识库、记忆机制、使用方式四个核心部分&#10;2. 功能重新定位: 从团队规范转为AI工具的专用执行指南&#10;3. 工作流标准化: 明确定义了从接收输入到生成输出的完整流程&#10;4. 输出格式规范: 统一了提交信息和代码审查报告的标准格式&#10;5. 使用指南完善: 区分最新提交和历史提交的不同修改方式" />
    <MESSAGE value="chore(docs): 删除提交规范文档&#10;&#10;移除了参考资料目录下的AI提交分析规范文档，该文件包含了AI工具的执行原则、知识库定义、记忆机制和使用方式等内容。&#10;&#10;1. 文件删除: 完全移除 参考资料/常用规则/提交规范.md 文件&#10;2. 内容清理: 删除了95行的AI工具执行指南内容&#10;3. 目录整理: 清理常用规则目录下的相关文档" />
    <MESSAGE value="refactor(docs): 重命名提交规范文档以准确反映其功能定位" />
    <MESSAGE value="refactor(docs): 重命名提交规范文档以准确反映其功能定位" />
    <MESSAGE value="docs: 添加AI助手核心规则和参考文档" />
    <MESSAGE value="docs: 添加AI助手核心规则和参考文档" />
    <MESSAGE value="docs: 新增AI开发规范文档集合并优化现有规则" />
    <MESSAGE value="docs: 新增AI开发规范文档集合并优化现有规则" />
    <MESSAGE value="add .cursor" />
    <MESSAGE value="feat(rules): 增加AI助手可用性监控与报告机制&#10;&#10;为两个AI助手规则文件添加了完整的可用性监控功能，提升用户体验和问题诊断能力。&#10;&#10;关键变更点：&#10;1. 身份定义增强：在核心身份中明确&quot;时刻关注自身可用性&quot;的职责&#10;2. 新增可用性报告原则：定义MCP工具连接失败时的统一处理流程&#10;3. 工作流产出完善：为计划、优化、评审阶段补充明确的产出说明&#10;4. 统一状态报告区块：在文件末尾添加标准化的可用性状态模板&#10;5. 文件重命名：简化协作式开发规则文件名，去除&quot;猫娘&quot;标识" />
    <MESSAGE value="feat(rules): 增加AI助手可用性监控与报告机制&#10;&#10;为两个AI助手规则文件添加了完整的可用性监控功能，提升用户体验和问题诊断能力。&#10;&#10;关键变更点：&#10;1. 身份定义增强：在核心身份中明确&quot;时刻关注自身可用性&quot;的职责&#10;2. 新增可用性报告原则：定义MCP工具连接失败时的统一处理流程&#10;3. 工作流产出完善：为计划、优化、评审阶段补充明确的产出说明&#10;4. 统一状态报告区块：在文件末尾添加标准化的可用性状态模板&#10;5. 文件重命名：简化协作式开发规则文件名，去除&quot;猫娘&quot;标识" />
    <MESSAGE value="update" />
    <option name="LAST_COMMIT_MESSAGE" value="update" />
  </component>
</project>